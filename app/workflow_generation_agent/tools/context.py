import json
import re
from typing import Any, Dict, Optional

import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from strands import tool
from urllib3.util.retry import Retry


# Constants
DEFAULT_TIMEOUT = 30
MAX_RETRIES = 3
BACKOFF_FACTOR = 0.3


def create_robust_session() -> requests.Session:
    """Create a requests session with retry strategy and timeout handling."""
    session = requests.Session()

    # Define retry strategy
    retry_strategy = Retry(
        total=MAX_RETRIES,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        backoff_factor=BACKOFF_FACTOR,
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


def normalize_name(name: Optional[str]) -> str:
    """
    Normalize a name by replacing spaces and hyphens with underscores.

    Args:
        name: The name to normalize. Can be None.

    Returns:
        Normalized name string. Returns empty string if input is None.

    Raises:
        TypeError: If name is not a string or None.
    """
    if name is None:
        return ""

    if not isinstance(name, str):
        raise TypeError(f"Expected string or None, got {type(name).__name__}")

    try:
        # Replace spaces and hyphens with underscores
        name = re.sub(r"[\s\-]+", "_", name)
        # Replace multiple underscores with single underscore
        name = re.sub(r"_+", "_", name)
        # Strip leading and trailing underscores
        name = name.strip("_")
        return name
    except Exception as e:
        return ""


def context_component(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch component context from the API and format it for display.

    Args:
        data: Dictionary containing 'category' and 'name' keys

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        return "Error: No data provided"

    if not isinstance(data, dict):
        return f"Error: Expected dictionary, got {type(data).__name__}"

    category = data.get("category")
    name_key = data.get("name")

    if not category:
        return "Error: Category is required"

    if not name_key:
        return "Error: Component name is required"

    url = "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    session = create_robust_session()

    try:
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()
        try:
            components = response.json()
        except json.JSONDecodeError as e:
            return "Error: Invalid JSON response from components API"

    except requests.exceptions.Timeout:
        return "Error: Request timeout while fetching components"
    except requests.exceptions.ConnectionError:
        return "Error: Failed to connect to components API"
    except requests.exceptions.HTTPError as e:
        return f"Error: HTTP {e.response.status_code} - Failed to fetch components"
    except Exception as e:
        return f"Error: Failed to fetch components - {str(e)}"

    # Validate components data structure
    if not isinstance(components, dict):
        return "Error: Invalid components data structure"

    if category not in components:
        available_categories = list(components.keys()) if components else []
        return f"Error: Category '{category}' not found. Available categories: {', '.join(available_categories)}"

    category_data = components[category]
    if not isinstance(category_data, dict):
        return f"Error: Invalid data structure for category '{category}'"

    if name_key not in category_data:
        available_components = list(category_data.keys()) if category_data else []
        return f"Error: Component '{name_key}' not found in category '{category}'. Available components: {', '.join(available_components)}"

    component = category_data[name_key]
    if not isinstance(component, dict):
        return f"Error: Invalid component data structure for '{name_key}'"

    # Extract component data with safe defaults
    name = component.get("name", "N/A")
    description = component.get("description", "No description")
    inputs = component.get("inputs", [])
    outputs = component.get("outputs", [])

    # Validate inputs and outputs are lists
    if not isinstance(inputs, list):
        inputs = []
    if not isinstance(outputs, list):
        outputs = []

    try:
        # Build context string safely
        context = f"Name : {name}\nDescription : {description}\nOriginalType : {name}\nType : Component\n"

        context += "Inputs :-\n"
        for i in inputs:
            if not isinstance(i, dict):
                continue

            context += f"Input Name : {i.get('name', 'N/A')}\n"
            context += f"Input Info : {i.get('info', '')}\n"
            context += f"Input Type : {i.get('input_type', '')}\n"

            input_types = i.get("input_types")
            if input_types and isinstance(input_types, list):
                try:
                    context += (
                        "Input Types : " + ", ".join(str(t) for t in input_types) + "\n"
                    )
                except Exception as e:
                    pass

            if i.get("required"):
                context += "Required\n"
            if i.get("is_handle"):
                context += "Handle\n"
            if i.get("is_list"):
                context += "List\n"
            if i.get("real_time_refresh"):
                context += "Real Time Refresh\n"
            if i.get("advanced"):
                context += "Advanced\n"
            if i.get("value") is not None:
                context += f"Default Value : {i['value']}\n"

            options = i.get("options")
            if options and isinstance(options, list):
                try:
                    context += (
                        "Options : " + ", ".join(str(opt) for opt in options) + "\n"
                    )
                except Exception as e:
                    pass
            if i.get("requirement_rules"):
                context += "Requirement Rules\n"
                context += "Input becomes required if "
                context += (" " + i.get("requirement_logic") + " ").join(
                    [
                        " ".join(
                            [
                                r.get("field_name"),
                                r.get("operator"),
                                r.get("field_value"),
                            ]
                        )
                        for r in i.get("requirement_rules")
                    ]
                )
            context += "\n"
            if i.get("visibility_rules"):
                context += "Visibility Rules\n"
                context += "Input becomes visible and if you want to use it, complete the following conditions:"
                context += (" " + i.get("visibility_logic") + " ").join(
                    [
                        " ".join(
                            [
                                r.get("field_name"),
                                r.get("operator"),
                                r.get("field_value"),
                            ]
                        )
                        for r in i.get("visibility_rules")
                    ]
                )

        context += "Outputs :-\n"
        for o in outputs:
            if not isinstance(o, dict):
                continue

            context += f"Output Name : {o.get('name', 'N/A')}\n"
            context += f"Output Type : {o.get('output_type', '')}\n"
            if o.get("semantic_type"):
                context += f"Semantic Type : {o['semantic_type']}\n"
            if o.get("method"):
                context += f"Method : {o['method']}\n"
            context += "\n"
        return context

    except Exception as e:
        return f"Error: Failed to build component context - {str(e)}"


def workflow_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch workflow context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' key for workflow ID

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        return "Error: No data provided"

    if not isinstance(data, dict):
        return f"Error: Expected dictionary, got {type(data).__name__}"

    workflow_id = data.get("id")
    if not workflow_id:
        return "Error: Workflow ID is required"

    if not isinstance(workflow_id, str):
        return f"Error: Workflow ID must be a string, got {type(workflow_id).__name__}"

    url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{workflow_id}"
    session = create_robust_session()

    try:
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()

        try:
            workflow_data = response.json()
        except json.JSONDecodeError as e:
            return "Error: Invalid JSON response from workflow API"

    except requests.exceptions.Timeout:
        return f"Error: Request timeout while fetching workflow {workflow_id}"
    except requests.exceptions.ConnectionError:
        return "Error: Failed to connect to workflow API"
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            return f"Error: Workflow '{workflow_id}' not found"
        return f"Error: HTTP {e.response.status_code} - Failed to fetch workflow"
    except Exception as e:
        return f"Error: Failed to fetch workflow - {str(e)}"

    # Validate workflow data structure
    if not isinstance(workflow_data, dict):
        return "Error: Invalid workflow data structure"

    workflow = workflow_data.get("workflow")
    if not workflow:
        return f"Error: No workflow found for ID '{workflow_id}'"

    if not isinstance(workflow, dict):
        return f"Error: Invalid workflow data structure for '{workflow_id}'"

    # Extract workflow data with safe defaults
    name = workflow.get("name", "N/A")
    description = workflow.get("description", "No description")
    inputs = workflow.get("start_nodes", [])

    # Validate inputs is a list
    if not isinstance(inputs, list):
        inputs = []

    # Define standard workflow outputs
    outputs = [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]

    try:
        # Build context string safely
        context = (
            f"Name : {name}\n"
            f"Description : {description}\n"
            f"OriginalType : workflow-{workflow_id}\n"
            f"Type : Workflow\n"
        )

        context += "Inputs :-\n"
        for i in inputs:
            if not isinstance(i, dict):
                continue

            field_name = i.get("field", "N/A")
            field_type = i.get("type", "N/A")
            context += f"Input Name : {field_name}\n"
            context += f"Input Info : {field_name}\n"
            context += f"Input Type : {field_type}\n"
            context += "Required\nHandle\n\n"

        context += "Outputs :-\n"
        for o in outputs:
            if not isinstance(o, dict):
                continue

            context += f"Output Name : {o.get('name', 'N/A')}\n"
            context += f"Output Type : {o.get('output_type', 'N/A')}\n\n"
        return context

    except Exception as e:
        return f"Error: Failed to build workflow context - {str(e)}"


def mcp_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch MCP context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' and 'name' keys for MCP ID and tool name

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        return "Error: No data provided"

    if not isinstance(data, dict):
        return f"Error: Expected dictionary, got {type(data).__name__}"

    mcp_id = data.get("id")
    tool_name = data.get("name")

    if not mcp_id:
        return "Error: MCP ID is required"

    if not tool_name:
        return "Error: Tool name is required"

    if not isinstance(mcp_id, str):
        return f"Error: MCP ID must be a string, got {type(mcp_id).__name__}"

    if not isinstance(tool_name, str):
        return f"Error: Tool name must be a string, got {type(tool_name).__name__}"

    url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{mcp_id}"
    session = create_robust_session()

    try:
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()

        try:
            mcp_data = response.json()
        except json.JSONDecodeError as e:
            return "Error: Invalid JSON response from MCP API"

    except requests.exceptions.Timeout:
        return f"Error: Request timeout while fetching MCP {mcp_id}"
    except requests.exceptions.ConnectionError:
        return "Error: Failed to connect to MCP API"
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            return f"Error: MCP '{mcp_id}' not found"
        return f"Error: HTTP {e.response.status_code} - Failed to fetch MCP"
    except Exception as e:
        return f"Error: Failed to fetch MCP - {str(e)}"

    # Validate MCP data structure
    if not isinstance(mcp_data, dict):
        return "Error: Invalid MCP data structure"

    mcp = mcp_data.get("mcp", {})
    if not isinstance(mcp, dict):
        return f"Error: Invalid MCP data structure for '{mcp_id}'"

    # Extract tools safely
    mcp_tools_config = mcp.get("mcp_tools_config", {})
    if not isinstance(mcp_tools_config, dict):
        mcp_tools_config = {}

    tools = mcp_tools_config.get("tools", [])
    if not isinstance(tools, list):
        tools = []

    # Find the specific tool
    tool = None
    for t in tools:
        if isinstance(t, dict) and t.get("name") == tool_name:
            tool = t
            break

    if not tool:
        available_tools = [
            t.get("name", "Unknown") for t in tools if isinstance(t, dict)
        ]
        return f"Error: Tool '{tool_name}' not found in MCP '{mcp_id}'. Available tools: {', '.join(available_tools)}"

    # Extract data safely
    mcp_name = mcp.get("name", "N/A")
    name = tool.get("name", "N/A")
    description = tool.get("description", "No description")
    input_schema = tool.get("input_schema", {})
    output_schema = tool.get("output_schema", {})

    # Validate schemas are dictionaries
    if not isinstance(input_schema, dict):
        input_schema = {}
    if not isinstance(output_schema, dict):
        output_schema = {}

    # Generate original_type safely (fix the NameError bug)
    try:
        original_type = "MCP_" + normalize_name(f"{mcp_name} - {name}")
    except Exception as e:
        # Fallback to simple concatenation
        safe_mcp_name = str(mcp_name).replace(" ", "_").replace("-", "_")
        safe_name = str(name).replace(" ", "_").replace("-", "_")
        original_type = f"MCP_{safe_mcp_name}_{safe_name}"

    try:
        # Build context string safely
        context = (
            f"Name : {name}\n"
            f"Description : {description}\n"
            f"OriginalType : {original_type}\n"
            f"Type : MCP\n"
            f"MCP_id : {mcp_id}\n"
            f"ToolName : {tool_name}\n"
        )

        context += "Inputs :-\n"

        # Extract schema components safely
        required_input = input_schema.get("required", [])
        if not isinstance(required_input, list):
            required_input = []

        defs = input_schema.get("$defs", {})
        if not isinstance(defs, dict):
            defs = {}

        properties = input_schema.get("properties", {})
        if not isinstance(properties, dict):
            properties = {}

        # Process each property safely
        for i, property_details in properties.items():
            if not isinstance(property_details, dict):
                continue

            context += f"Input Name : {i}\n"
            context += f"Input Info : {property_details.get('description', '')}\n"

            property_type = property_details.get("type", "Any")
            options = None
            property_scheme = None
            items = None
            is_array = False

            try:
                if property_type:
                    context += f"Input Type : {property_type}\n"
                    if property_type == "object":
                        property_scheme = property_details.get("properties", {})
                        if not isinstance(property_scheme, dict):
                            property_scheme = {}
                    if property_type == "array":
                        items = property_details.get("items", [])
                        is_array = True

                elif "anyOf" in property_details:
                    any_of = property_details["anyOf"]
                    if isinstance(any_of, list):
                        context += "Input Type : "
                        for j in any_of:
                            if not isinstance(j, dict):
                                continue
                            if "type" in j and j["type"] != "null":
                                context += j["type"] + "\n"
                                if j["type"] == "object":
                                    property_scheme = j.get("properties", {})
                                    if not isinstance(property_scheme, dict):
                                        property_scheme = {}
                                if j["type"] == "array":
                                    items = j.get("items")
                                    is_array = True
                            elif "$ref" in j:
                                try:
                                    ref_key = j["$ref"].split("/")[-1]
                                    ref = defs.get(ref_key, {})
                                    if isinstance(ref, dict):
                                        context += ref.get("type", "N/A") + "\n"
                                        if "enum" in ref and isinstance(
                                            ref["enum"], list
                                        ):
                                            options = ref["enum"]
                                        if ref.get("type") == "object":
                                            property_scheme = ref.get("properties", {})
                                            if not isinstance(property_scheme, dict):
                                                property_scheme = {}
                                        if ref.get("type") == "array":
                                            items = ref.get("items")
                                            is_array = True
                                except Exception as e:
                                    pass

                elif "$ref" in property_details:
                    try:
                        ref_key = property_details["$ref"].split("/")[-1]
                        ref = defs.get(ref_key, {})
                        if isinstance(ref, dict):
                            context += f"Input Type : {ref.get('type', 'N/A')}\n"
                            if "enum" in ref and isinstance(ref["enum"], list):
                                options = ref["enum"]
                            if ref.get("type") == "object":
                                property_scheme = ref.get("properties", {})
                                if not isinstance(property_scheme, dict):
                                    property_scheme = {}
                            if ref.get("type") == "array":
                                items = ref.get("items", [])
                                is_array = True
                    except Exception as e:
                        pass

            except Exception as e:
                continue

            # Add property metadata safely
            if i in required_input:
                context += "Required\n"
            context += "Handle\n"
            if is_array:
                context += "List\n"
            if property_details.get("default") is not None:
                context += f"Default Value : {property_details['default']}\n"
            if options:
                try:
                    context += (
                        "Options : " + ", ".join(str(opt) for opt in options) + "\n"
                    )
                except Exception as e:
                    pass

            # Handle nested properties safely
            if property_scheme and isinstance(property_scheme, dict):
                context += "Properties :-\n"
                for j, prop in property_scheme.items():
                    if not isinstance(prop, dict):
                        continue
                    context += f"> Property Name : {j}\n"
                    context += f"> Property Info : {prop.get('description', '')}\n"
                    if prop.get("type"):
                        context += f"> Property Type : {prop['type']}\n"
                    if prop.get("anyOf") and isinstance(prop["anyOf"], list):
                        context += "> Property Type : "
                        for k in prop["anyOf"]:
                            if (
                                isinstance(k, dict)
                                and "type" in k
                                and k["type"] != "null"
                            ):
                                context += k["type"] + ", "
                        context += "\n"
                    if prop.get("default") is not None:
                        context += f"> Property Default Value : {prop['default']}\n"
                    if prop.get("enum") and isinstance(prop["enum"], list):
                        try:
                            context += (
                                "> Property Options : "
                                + ", ".join(str(e) for e in prop["enum"])
                                + "\n"
                            )
                        except Exception as e:
                            pass
                    context += "> \n"

            if items:
                context += f"Items : {items}\n"
            context += "\n"

        # Process outputs safely
        context += "Outputs :-\n"
        if not output_schema:
            output_schema = {
                "properties": {
                    "result": {"name": "result", "type": "Any", "description": "Result"}
                }
            }

        output_properties = output_schema.get("properties", {})
        if isinstance(output_properties, dict):
            for o, details in output_properties.items():
                if not isinstance(details, dict):
                    logger.warning(f"Skipping invalid output '{o}': {type(details)}")
                    continue
                context += f"Output Name : {o}\n"
                context += f"Output Info : {details.get('description', '')}\n"
                context += f"Output Type : {details.get('type', '')}\n\n"
        return context

    except Exception as e:
        return f"Error: Failed to build MCP context - {str(e)}"


context_helpers = {
    "component": context_component,
    "workflow": workflow_context,
    "mcp": mcp_context,
}


@tool(
    name="get_context",
    description="Function take a item return by the RAG search and as a dictionary not the string and return the context, inputs and output of the node.",
)
def get_context(node_info: dict) -> str:
    return context_helpers[node_info["type"]](node_info)
